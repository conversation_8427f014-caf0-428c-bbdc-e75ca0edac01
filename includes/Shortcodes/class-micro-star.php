<?php

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

/**
 * Micro Star shortcode
 */
class Reviewkit_Shortcodes_Micro_Star extends Reviewkit_Shortcodes_Base_Shortcode {

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->tag = 'reviewkit_tp_micro_star';

		// Get dynamic business data
		$business_data = Reviewkit_Business_Data::get_business_data();
		$trust_score = $business_data['trustScore'] ?? $business_data['trust_score'] ?? 4.8;

		$this->default_attributes = [
			'rating' => Reviewkit_Business_Data::get_rating_text( $trust_score ),
			'stars'  => Reviewkit_Business_Data::get_rounded_trust_score( $trust_score ),
			'url'    => $business_data['trustpilot_url'] ?? '#',
			'star_image' => Reviewkit_Business_Data::get_star_image_url( $trust_score ),
			'single_star' => Reviewkit_Business_Data::get_single_star_image_url(),
		];
	}

	/**
	 * Generate the HTML output
	 *
	 * @param array $atts Sanitized attributes
	 * @param string $content Shortcode content
	 * @return string
	 */
	protected function generate_html( $atts, $content ) {
		$rating = $this->default_attributes['rating'];
		$stars = intval( $this->default_attributes['stars'] );
		$url = $atts['url'];


		// $html = sprintf('<div class="reviewkit_fpln_mcs reviewkit_fpln_common preview">
		// 		<div class="dynamic_rating">Excellent</div>
		// 		<div class="dynamic_stars">
		// 			<img src="http://stark.test/wp-content/plugins/trustpilot-reviewkit//assets/images/stars-5_0.svg" alt="Excellent star rating" style="width: 100%;">
		// 		</div>
		// 		<div class="tp-wrapper">
		// 			<img src="http://stark.test/wp-content/plugins/trustpilot-reviewkit//assets/images/single-star.svg" alt="Trustpilot Logo">
		// 			<div class="place_name">Trustpilot</div>
		// 		</div>
		// 	</div>',
		// 	''
		// );

		// return $html;


		// $html = '<div class="reviewkit_fpln_mcs reviewkit_fpln_common reviewkit-frontend">';
		// $html .= '</div>';
		$html = '';
		ob_start();
		?>
			<div class="reviewkit_fpln_mcs reviewkit_fpln_common frontend">
				<div class="dynamic_rating"><?php echo esc_html( $rating ); ?></div>
				<div class="dynamic_stars">
					<img src="<?php echo esc_url( $this->default_attributes['star_image'] ); ?>" alt="<?php echo esc_attr( $rating . ' star rating' ); ?>" style="width: 100%;">
				</div>
				<div class="tp-wrapper">
					<img src="<?php echo esc_url( $this->default_attributes['single_star'] ); ?>" alt="Trustpilot Logo">
					<div class="place_name"><?php echo esc_html( 'Trustpilot' ); ?></div>
				</div>
			</div>
		<?php

		$html = ob_get_clean();

		// // Left section with rating and stars
		// $html .= '<div class="reviewkit_fpln_mc_inner_left">';
		// $html .= '<span class="review_us_one">' . esc_html( $rating ) . '</span>';
		// $html .= '<div class="reviewkit_star_rating">';
		// $html .= '<img src="' . esc_url( $this->default_attributes['star_image'] ) . '" alt="' . esc_attr( $rating_text . ' rating' ) . '">';
		// $html .= '</div>';
		// $html .= '</div>';
		
		// // Right section with Trustpilot logo
		// $html .= '<div class="reviewkit_fpln_mc_inner_right">';
		// $html .= '<span class="review_us_one"></span>';
		// $html .= '<img src="' . esc_url( $this->default_attributes['single_star'] ) . '" alt="Trustpilot logo"  style="width: 20px;" />';
		// $html .= '<span class="place_name">Trustpilot</span>';
		// $html .= '</div>';
		
		// $html .= '</div>';

		return $html;
	}
}
