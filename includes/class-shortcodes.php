<?php

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

/**
 * Main Shortcodes class to register and manage all Trustpilot shortcodes
 */
class Reviewkit_Shortcodes {

	/**
	 * Array of shortcode instances
	 *
	 * @var array
	 */
	public $shortcodes = [];

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->init_shortcodes();
}

	/**
	 * Initialize all shortcodes
	 */
	public function init_shortcodes() {
		$this->shortcodes = [
			'mini'             => new Reviewkit_Shortcodes_Mini_Trustbox(),
			'starter'          => new Reviewkit_Shortcodes_Starter_Trustbox(),
			'micro_trustscore' => new Reviewkit_Shortcodes_Micro_Trust_Score(),
			'micro_star'       => new Reviewkit_Shortcodes_Micro_Star(),
			'micro_reviewcount' => new Reviewkit_Shortcodes_Micro_Review_Count(),
			'micro_button'     => new Reviewkit_Shortcodes_Micro_Button(),
			'micro_combo'      => new Reviewkit_Shortcodes_Micro_Combo(),
		];

		// Register each shortcode
		foreach ( $this->shortcodes as $key => $shortcode ) {
			$shortcode->register();
		}
	}

	public function enqueue_shortcode_assets() {

	}

	/**
	 * Check if any of our shortcodes are being used on the current page
	 *
	 * @return bool
	 */
	public static function has_shortcodes_on_page() {
		global $post;

		if ( ! $post ) {
			return false;
		}

		$shortcode_tags = [
			'reviewkit_tp_mini',
			'reviewkit_tp_starter',
			'reviewkit_tp_micro_trustscore',
			'reviewkit_tp_micro_star',
			'reviewkit_tp_micro_reviewcount',
			'reviewkit_tp_micro_button',
			'reviewkit_tp_micro_combo',
		];

		foreach ( $shortcode_tags as $tag ) {
			if ( has_shortcode( $post->post_content, $tag ) ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Get all registered shortcodes
	 *
	 * @return array
	 */
	public function get_shortcodes() {
		return $this->shortcodes;
	}

	/**
	 * Get shortcode information for admin interface
	 *
	 * @return array
	 */
	public function get_shortcodes_info() {
		return [
			[
				'name'        => 'Mini Trustbox',
				'shortcode'   => '[reviewkit_tp_mini]',
				'description' => 'Compact widget showing TrustScore, star rating, and review count',
				'category'    => 'free',
			],
			[
				'name'        => 'Starter Trustbox',
				'shortcode'   => '[reviewkit_tp_starter]',
				'description' => 'Interactive widget with star rating and tooltip',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro TrustScore',
				'shortcode'   => '[reviewkit_tp_micro_trustscore]',
				'description' => 'Simple display of TrustScore rating',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Star',
				'shortcode'   => '[reviewkit_tp_micro_star]',
				'description' => 'Interactive star rating display',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Review Count',
				'shortcode'   => '[reviewkit_tp_micro_reviewcount]',
				'description' => 'Link showing review count',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Button',
				'shortcode'   => '[reviewkit_tp_micro_button]',
				'description' => 'Button-style widget with review count',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Combo',
				'shortcode'   => '[reviewkit_tp_micro_combo]',
				'description' => 'Combined star rating and review count widget',
				'category'    => 'free',
			],
		];
	}
}
