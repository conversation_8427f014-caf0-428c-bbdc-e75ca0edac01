<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit36a8b2be395700ade2a920940c66c802
{
    public static $prefixLengthsPsr4 = array (
        'G' => 
        array (
            'Gutensuite\\Reviewkit\\' => 21,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Gutensuite\\Reviewkit\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit36a8b2be395700ade2a920940c66c802::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit36a8b2be395700ade2a920940c66c802::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit36a8b2be395700ade2a920940c66c802::$classMap;

        }, null, ClassLoader::class);
    }
}
