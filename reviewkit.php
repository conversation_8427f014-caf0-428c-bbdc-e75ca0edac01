<?php
/**
 * Plugin Name:       Reviewkit
 * Description:       Automatically sync and display your Trustpilot reviews on your WordPress site with customizable widgets, trust badges, and real-time updates. Boost credibility and conversions by showcasing genuine customer testimonials anywhere on your site with easy shortcodes and flexible layouts.
 * Version:           1.0.0
 * Requires at least: 6.8
 * Requires PHP:      7.4
 * Author:            GutenSuite
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       reviewkit
 *
 * @package Reviewkit
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if( ! file_exists( __DIR__ . '/includes/class-autoloader.php' ) ){
	exit;
}
require_once __DIR__ . '/includes/class-autoloader.php';

// Initialize the autoloader
$autoloader = new Reviewkit_Autoloader( __DIR__ . '/includes' );
$autoloader->register();

if ( ! class_exists( 'Reviewkit' ) ) {
	final class Reviewkit {

		private function __construct() {
			$this->define_constants();
			register_activation_hook( __FILE__, array( $this, 'activate' ) );
			register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
			add_action( 'init', array( $this, 'on_plugins_loaded' ) );
			add_action( 'main_plugin_loaded', array( $this, 'init_plugin' ) );
		}

		public static function init() {
			static $instance = false;

			if ( ! $instance ) {
				$instance = new self();
			}

			return $instance;
		}
		public function define_constants() {
			/**
			 * Defines CONSTANTS for Whole plugins.
			 */
			define( 'REVIEWKIT_SLUG', 'reviewkit' );
			define( 'REVIEWKIT_PLUGIN_ROOT_URI', plugins_url( '/', __FILE__ ) );
			define( 'REVIEWKIT_ROOT_DIR_PATH', plugin_dir_path( __FILE__ ) );
			define( 'REVIEWKIT_ASSETS_DIR_PATH', REVIEWKIT_ROOT_DIR_PATH . 'assets/' );
			define( 'REVIEWKIT_ASSETS_URI', REVIEWKIT_PLUGIN_ROOT_URI . 'assets/' );
			define( 'REVIEWKIT_TP_API', 'https://api.gutensuite.net/data/trustpilot/v1/reviews/' );
		}


		public function on_plugins_loaded() {
			do_action( 'main_plugin_loaded' );
		}

		/**
		 * Initialize the plugin
		 *
		 * @return void
		 */
		public function init_plugin() {
			if ( is_admin() ) {
				new Reviewkit_Admin();
			}
			new Reviewkit_Admin_Assets();

			Reviewkit_Business_Data::init_default_data();

			new Reviewkit_Shortcodes();
		}


		public function load_textdomain() {
			// load_plugin_textdomain('reviewkit', false, dirname(plugin_basename(__FILE__)) . '/languages/');
		}


		public function activate() {}

		public function deactivate() {}
	}
}

/**
 * Initializes the main plugin
 *
 * @return \Reviewkit
 */
if ( ! function_exists( 'Reviewkit_Start' ) ) {
	function Reviewkit_Start() {
		return Reviewkit::init();
	}
}

// Plugin Start
Reviewkit_Start();
