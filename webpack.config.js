const defaultConfig = require("@wordpress/scripts/config/webpack.config");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const path = require("path");
const { exit } = require("process");

const config = {
	...defaultConfig,
	entry: {
		"reviewkit.core.min": path.resolve(__dirname, "react_app/index.js"),
		"shortcode.core.min": path.resolve(
			__dirname,
			"react_app/assets/scss/previews/main.scss"
		),
	},
	output: {
		path: path.resolve(__dirname, "assets/js"),
		filename: "[name].js",
		// CSS will be output by MiniCssExtractPlugin, see below
	},
	plugins: [
		...defaultConfig.plugins,
		new CleanWebpackPlugin(),
		new MiniCssExtractPlugin({
			filename: ({ chunk }) => {
				if (chunk.name === "shortcode.core.min") {
					return "./shortcode/index.min.css";
				}
				return "[name].css";
			},
		}),
	],
	externals: {
		jquery: "jQuery",
		underscore: "_",
		lodash: "lodash",
		react: ["vendor", "React"],
		"react-dom": ["vendor", "ReactDOM"],
		// WordPress dependencies.
		"@wordpress/i18n": ["vendor", "wp", "i18n"],
		"@wordpress/hooks": ["vendor", "wp", "hooks"],
		"@wordpress/components": ["vendor", "wp", "components"],
	},
};

module.exports = config;
