import React from "react";
import StarRating from "./StarRating";
import CustomerInfo from "./CustomerInfo";
import { getRatingText, getStarImageUrl } from "../../helper/utils";

/**
 * ReviewCard component for displaying individual review
 * @param {Object} review - Review object
 * @param {string} review.reviewId - Review ID
 * @param {number} review.rating - Review rating (1-5)
 * @param {string} review.reviewTitle - Review title
 * @param {string} review.reviewBody - Review content
 * @param {Object} review.customer - Customer information
 * @param {Object} review.dates - Review dates
 * @param {string} review.reviewUrl - Review URL on Trustpilot
 */
const ReviewCard = ({ review = {} }) => {
	const {
		reviewId,
		rating = 0,
		reviewTitle = "",
		reviewBody = "",
		customer = {},
		dates = {},
		reviewUrl = "",
	} = review;

	const { publishedDate, experiencedDate } = dates;

	// Format date for display
	const formatDate = (dateString) => {
		if (!dateString) return "";
		try {
			const date = new Date(dateString);
			return date.toLocaleDateString("en-US", {
				year: "numeric",
				month: "short",
				day: "numeric",
			});
		} catch (error) {
			return "";
		}
	};

	const dynamicRating = getRatingText(rating);

	// Truncate long review text
	const truncateText = (text, maxLength = 500) => {
		if (!text || text.length <= maxLength) return text;
		return text.substring(0, maxLength) + "...";
	};

	return (
		<div className="review-card" data-review-id={reviewId}>
			<div className="review-header">
				<CustomerInfo customer={customer} size="small" />
				<div className="review-meta">
					<div className="dynamic_stars" style={{ width: "150px" }}>
						<img
							src={getStarImageUrl(review.rating)}
							alt={`${dynamicRating} star rating`}
							style={{ width: "100%" }}
						/>
					</div>
					{publishedDate && (
						<span className="review-date">{formatDate(publishedDate)}</span>
					)}
				</div>
			</div>

			<div className="review-content">
				{reviewTitle && <h4 className="review-title">{reviewTitle}</h4>}
				{reviewBody && (
					<p className="review-body">{truncateText(reviewBody)}</p>
				)}
				{experiencedDate && (
					<div className="review-experience-date">
						<small>Experience date: {formatDate(experiencedDate)}</small>
					</div>
				)}
			</div>

			{reviewUrl && (
				<div className="review-footer">
					<a
						href={reviewUrl}
						target="_blank"
						rel="noopener noreferrer"
						className="review-link"
					>
						View on Trustpilot
					</a>
				</div>
			)}
		</div>
	);
};

export default ReviewCard;
