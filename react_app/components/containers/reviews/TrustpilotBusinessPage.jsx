import { getRatingText, getStarImageUrl } from "../../helper/utils";
import { __ } from "@wordpress/i18n";

/**
 * TrustpilotBusinessPage component for displaying business information
 * @param {Object} businessDetails - Business details object from API response
 */
const TrustpilotBusinessPage = ({ businessDetails = {} }) => {
	const {
		displayName = "",
		identifyingName = "",
		numberOfReviews = 0,
		trustScore = 0,
		websiteUrl = "",
		profileImageUrl = "",
		stars = 0,
		categories = [],
		activity = {},
	} = businessDetails;

	// Format large numbers
	const formatNumber = (num) => {
		if (num >= 1000000) {
			return (num / 1000000).toFixed(1) + "M";
		} else if (num >= 1000) {
			return (num / 1000).toFixed(1) + "K";
		}
		return num.toString();
	};

	const primaryCategory = categories.find((cat) => cat.isPrimary)?.name || "";

	const isVerified = activity?.verification?.verifiedUserIdentity || false;
	const isClaimed = activity?.isClaimed || false;

	const dynamicRating = getRatingText(trustScore);

	// Format profile image URL
	const getImageUrl = (url) => {
		if (!url) return "";
		if (url.startsWith("//")) {
			return `https:${url}`;
		}
		return url;
	};

	if (!displayName) {
		return (
			<div className="trustpilot-business-page">
				<div className="business-placeholder">
					<p>{__("No business details found", "reviewkit")}</p>
				</div>
			</div>
		);
	}

	return (
		<div className="trustpilot-business-page">
			<div className="business-header">
				<div className="business-logo">
					{profileImageUrl ? (
						<img
							src={getImageUrl(profileImageUrl)}
							alt={`${displayName} logo`}
							className="business-image"
							onError={(e) => {
								e.target.style.display = "none";
								e.target.nextSibling.style.display = "flex";
							}}
						/>
					) : null}
					<div
						className="business-initials"
						style={{ display: profileImageUrl ? "none" : "flex" }}
					>
						{displayName.charAt(0).toUpperCase()}
					</div>
				</div>

				<div className="business-info">
					<div className="business-name-section">
						<h2 className="business-name">{displayName}</h2>
						{isClaimed && (
							<span className="business-claimed">
								✓ {__("Claimed", "reviewkit")}
							</span>
						)}
						{isVerified && (
							<span className="business-verified">
								✓ {__("Verified", "reviewkit")}
							</span>
						)}
					</div>

					{identifyingName && (
						<p className="business-website">{identifyingName}</p>
					)}

					{primaryCategory && (
						<p className="business-category">{primaryCategory}</p>
					)}

					<div className="business-rating">
						<div className="rating-percentage">
							<div className="dynamic_stars" style={{ width: "200px" }}>
								<img
									src={getStarImageUrl(trustScore)}
									alt={`${dynamicRating} star rating`}
								/>
							</div>
							<span>{trustScore}</span>
						</div>
						<span className="review-count">
							{__("Based on", "reviewkit")} {formatNumber(numberOfReviews)}{" "}
							{__("reviews", "reviewkit")}
						</span>
					</div>

					{websiteUrl && (
						<div className="business-links">
							<a
								href={websiteUrl}
								target="_blank"
								rel="noopener noreferrer"
								className="components-button is-secondary business-website-link"
							>
								{__("Visit Website", "reviewkit")}
							</a>
						</div>
					)}
				</div>
			</div>

			{activity?.replyBehavior && (
				<div className="business-stats">
					<div className="stat-item">
						<span className="stat-label">{__("Reply Rate", "reviewkit")}:</span>
						<span className="stat-value">
							{activity?.replyBehavior?.replyPercentage?.toFixed(2)}%
						</span>
					</div>
					{activity.replyBehavior?.averageDaysToReply >= 0 && (
						<div className="stat-item">
							<span className="stat-label">
								{__("Avg. Reply Time", "reviewkit")}:
							</span>
							<span className="stat-value">
								{Math.round(activity.replyBehavior.averageDaysToReply)}{" "}
								{__("days", "reviewkit")}
							</span>
						</div>
					)}
				</div>
			)}
		</div>
	);
};

export default TrustpilotBusinessPage;
