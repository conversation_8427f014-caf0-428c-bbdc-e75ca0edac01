=== Reviewkit ===
Contributors: gutensuite
Tags: trustpilot, reviews, testimonials, trust badges, social proof
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html


== Description ==

Automatically sync and showcase Trustpilot widgets and reviews on WordPress to engage or attract your customers using customizable badges and live updates.


**Instantly Boost Trust & Sales with Trustpilot Reviews**

For businesses big and small, customer trust is the ultimate currency — and your Trustpilot reviews are the goldmine to earn it. 93% of consumers say online reviews influence their purchase decisions, so showcasing those genuine testimonials on your site is like having a marketing team working 24/7.

Reviewkit makes it effortless to tap into that goldmine with a single click. Our WordPress plugin automatically syncs all your Trustpilot feedback in real-time and displays it beautifully across your site (no developer needed!).

**Key Features:**

* **Automatic Real-Time Sync:** New Trustpilot reviews flow into WordPress instantly, so your testimonial feed is always up-to-date without any manual effort.
* **Customizable Review Widgets:** Choose from list, grid, carousel or badge layouts and style them to match your brand. Each widget highlights star ratings, reviewer names and feedback to make reviews eye-catching.
* **Trust & Rating Badges:** Display your TrustScore, star rating, and review count with sleek badge widgets. These instant trust seals boost credibility at a glance.
* **Advanced Filtering:** Lets you highlight your best feedback. Filter reviews by star rating or date to display only 5-star ratings, top feedback quotes, or the newest testimonials.
* **Easy Setup & Flexibility:** Just install and connect your Trustpilot business account — no API keys or coding required. Then use shortcodes or widgets to place reviews anywhere, from homepage to product pages.

**Showcase Your Reviews Anywhere**

Unlimited layout options let you present your Trustpilot feedback with style. Use list, grid or carousel widgets to match your site's design. Each review displays star ratings, reviewer names and comments — creating irresistible social proof for visitors. Fully responsive, the widgets look great on any device and blend seamlessly with your theme.

**Leverage Social Proof for Conversions**

Customers trust other customers. In fact, 75% of consumers search for reviews before buying, and review-driven promotions get 4× more clicks than ads without reviews. By putting real Trustpilot testimonials in front of your visitors, you turn casual browsers into confident buyers.

**Simple Setup & Total Control**

No tech skills? No problem. Activate the plugin in minutes and connect your Trustpilot profile with a click. Use intuitive settings to adjust colors, fonts, and layouts. Place reviews anywhere using built-in shortcodes or Gutenberg blocks.

**Available Shortcodes:**

* `[reviewkit_tp_mini]` - Mini Trustbox widget
* `[reviewkit_tp_starter]` - Starter Trustbox widget
* `[reviewkit_tp_micro_trustscore]` - Micro Trust Score badge
* `[reviewkit_tp_micro_star]` - Micro Star rating
* `[reviewkit_tp_micro_reviewcount]` - Micro Review Count
* `[reviewkit_tp_micro_button]` - Micro Button widget
* `[reviewkit_tp_micro_combo]` - Micro Combo widget

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/reviewkit` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Navigate to the Reviewkit settings page in your WordPress admin dashboard.
4. Connect your Trustpilot business account by following the setup wizard.
5. Configure your display preferences and styling options.
6. Use shortcodes or widgets to display reviews anywhere on your site.

== Frequently Asked Questions ==

= Do I need a Trustpilot account to use this plugin? =

Yes, you need an active Trustpilot business account with reviews to use this plugin. The plugin syncs and displays reviews from your Trustpilot profile.

= Will this plugin slow down my website? =

No, Reviewkit is optimized for performance. Reviews are cached and loaded efficiently to ensure minimal impact on your site's speed.

= Can I customize the appearance of the review widgets? =

Yes, the plugin includes extensive customization options including colors, fonts, layouts, and styling to match your brand and theme.

= How often do reviews sync from Trustpilot? =

Reviews sync in real-time automatically. New reviews from your Trustpilot account will appear on your site without any manual intervention.

= Can I filter which reviews are displayed? =

Yes, you can filter reviews by star rating, date, and other criteria to showcase only your best testimonials or most recent feedback.

= Is the plugin mobile-friendly? =

Absolutely! All review widgets are fully responsive and look great on desktop, tablet, and mobile devices.

= Do I need coding skills to use this plugin? =

No coding skills required! The plugin features an intuitive interface and simple shortcodes that anyone can use.

= Can I display reviews on multiple pages? =

Yes, you can use shortcodes or widgets to display reviews on any page, post, or widget area throughout your site.

== Screenshots ==

1. Plugin settings page with Trustpilot connection setup
2. Mini Trustbox widget displaying customer reviews
3. Trust badge showing star rating and review count
4. Customization options for styling and layout
5. Multiple widget layouts: list, grid, and carousel views
6. Mobile-responsive review displays

== Changelog ==

= 1.0.0 =
* Initial release
* Automatic Trustpilot review synchronization
* Multiple customizable widget layouts
* Trust badges and rating displays
* Advanced filtering options
* Shortcode support for easy placement
* Mobile-responsive design
* WordPress 6.8+ compatibility
* Performance optimizations

== Upgrade Notice ==

= 1.0.0 =
Initial release of Reviewkit - the easiest way to display your Trustpilot reviews on WordPress.
